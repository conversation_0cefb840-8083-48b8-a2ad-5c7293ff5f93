# MCP Shrimp Task Manager Environment Variables Configuration Example
# Copy this file to .env and customize as needed

# ============================
# Basic Configuration
# ============================

# Task data storage directory (absolute path)
DATA_DIR=

# Set Templates
# Specifies the template set to use for prompts and WebGUI language.
# Default is 'en'. Currently available options are 'en' and 'zh'.
# When using custom templates, WebGUI will default to English interface.
# To use custom templates, copy the 'src/prompts/templates_en' directory
# to the location specified by DATA_DIR, rename the copied directory
# (e.g., to 'my_templates'), and set TEMPLATES_USE to the new directory name (e.g., 'my_templates').
TEMPLATES_USE=en

# Enable GUI interface
# If set to true, a file named 'WebGUI.md' will be created in the DATA_DIR.
# This file will contain a local web address you can open in your browser to access the GUI.
# Default is false.
ENABLE_GUI=false

# WebGUI port
WEB_PORT=3000

# ============================
# Prompt Customization
# ============================

# Task Planning (planTask) - Complete Override
# MCP_PROMPT_PLAN_TASK=

# Task Planning (planTask) - Append Content
# MCP_PROMPT_PLAN_TASK_APPEND=

# Task Analysis (analyzeTask) - Complete Override
# MCP_PROMPT_ANALYZE_TASK=

# Task Analysis (analyzeTask) - Append Content
# MCP_PROMPT_ANALYZE_TASK_APPEND=

# Solution Evaluation (reflectTask) - Complete Override or Append
# MCP_PROMPT_REFLECT_TASK=
# MCP_PROMPT_REFLECT_TASK_APPEND=

# Task Breakdown (splitTasks) - Complete Override or Append
# MCP_PROMPT_SPLIT_TASKS=
# MCP_PROMPT_SPLIT_TASKS_APPEND=

# Task Execution (executeTask) - Complete Override or Append
# MCP_PROMPT_EXECUTE_TASK=
# MCP_PROMPT_EXECUTE_TASK_APPEND=

# Task Verification (verifyTask) - Complete Override or Append
# MCP_PROMPT_VERIFY_TASK=
# MCP_PROMPT_VERIFY_TASK_APPEND=

# Task Completion (completeTask) - Complete Override or Append
# MCP_PROMPT_COMPLETE_TASK=
# MCP_PROMPT_COMPLETE_TASK_APPEND=

# List Tasks (listTasks) - Complete Override
# MCP_PROMPT_LIST_TASKS=

# Query Task (queryTask) - Complete Override or Append
# MCP_PROMPT_QUERY_TASK=
# MCP_PROMPT_QUERY_TASK_APPEND=

# Get Task Details (getTaskDetail) - Complete Override or Append
# MCP_PROMPT_GET_TASK_DETAIL=
# MCP_PROMPT_GET_TASK_DETAIL_APPEND= 