name: Publish to npm

on:
  push:
    tags:
      - "v*" # 只有 push tag 才會觸發

jobs:
  release:
    runs-on: ubuntu-latest
    environment: NPM
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          registry-url: "https://registry.npmjs.org/"

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Publish to npm
        run: npm publish --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
