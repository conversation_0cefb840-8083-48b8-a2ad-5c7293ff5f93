/* MCP Shrimp Task Manager 網站樣式 */

/* 全局變量 */
:root {
  --primary-color: #3b82f6;
  --primary-dark: #2563eb;
  --secondary-color: #4f46e5;
  --text-color: #1f2937;
  --text-light: #f9fafb;
  --gray-light: #f3f4f6;
  --gray-medium: #9ca3af;
  --gray-dark: #374151;
  --transition-speed: 0.3s;
  --pain-red: #ef4444;
  --pain-orange: #f97316;
  --pain-blue: #3b82f6;
  --solution-green: #10b981;
  --solution-yellow: #ca8a04;
  --solution-purple: #8b5cf6;
}

/* 全局樣式 */
html {
  scroll-behavior: smooth;
  overflow-x: hidden; /* 防止水平溢出 */
}

body {
  line-height: 1.6;
  overflow-x: hidden; /* 防止水平溢出 */
}

/* 自定義動畫 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes floatAnimation {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes pulseArrow {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(6px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes rotateIcon {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(10deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 導航欄樣式 */
header nav img {
  transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

header nav img:hover {
  transform: scale(1.2) rotate(10deg);
}

header nav a {
  position: relative;
  transition: all 0.3s ease;
}

header nav a::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -3px;
  left: 0;
  background-color: var(--primary-color);
  transition: width 0.3s;
}

header nav a:hover::after {
  width: 100%;
}

#mobile-menu {
  transition: transform 0.4s ease-in-out, opacity 0.3s ease;
  transform-origin: top;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
}

#mobile-menu.visible {
  opacity: 1;
  max-height: 500px;
}

/* 英雄區樣式 */
#hero {
  position: relative;
  overflow: hidden;
}

#hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.4;
  pointer-events: none;
}

#hero .btn-primary {
  animation: pulse 2.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

#hero img {
  filter: drop-shadow(0 4px 15px rgba(0, 0, 0, 0.3));
  animation: floatAnimation 6s ease-in-out infinite;
  transition: all 0.5s ease;
}

#hero img:hover {
  transform: scale(1.05) translateY(-5px);
  filter: drop-shadow(0 8px 25px rgba(0, 0, 0, 0.5));
}

#hero .absolute {
  animation: floatAnimation 8s ease-in-out infinite;
  animation-delay: calc(var(--animation-order) * 0.7s);
  opacity: 0;
  animation-fill-mode: forwards;
}

/* 痛點與解決方案區樣式 */
#pain-points .shadow-lg {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

#pain-points .shadow-lg:hover {
  transform: translateY(-15px);
  box-shadow: 0 25px 30px -5px rgba(0, 0, 0, 0.15),
    0 10px 15px -5px rgba(0, 0, 0, 0.08);
}

#pain-points .shadow-lg:hover img {
  transform: scale(1.1) rotate(5deg);
}

#pain-points img {
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

#pain-points .h-8.w-8.text-green-500 {
  animation: pulseArrow 2s ease-in-out infinite;
}

#pain-points .rounded-lg.overflow-hidden:nth-child(1) {
  border-bottom: 3px solid var(--pain-red);
}

#pain-points .rounded-lg.overflow-hidden:nth-child(2) {
  border-bottom: 3px solid var(--pain-orange);
}

#pain-points .rounded-lg.overflow-hidden:nth-child(3) {
  border-bottom: 3px solid var(--pain-blue);
}

#pain-points .bg-green-50,
#pain-points .bg-yellow-50,
#pain-points .bg-purple-50 {
  position: relative;
  overflow: hidden;
}

#pain-points .bg-green-50::before,
#pain-points .bg-yellow-50::before,
#pain-points .bg-purple-50::before {
  content: "";
  position: absolute;
  top: -20px;
  right: -20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  opacity: 0.1;
}

#pain-points .bg-green-50::before {
  background-color: var(--solution-green);
}

#pain-points .bg-yellow-50::before {
  background-color: var(--solution-yellow);
}

#pain-points .bg-purple-50::before {
  background-color: var(--solution-purple);
}

/* 按鈕樣式 */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 600;
  text-align: center;
  transition: all var(--transition-speed);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
  background-color: white;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background-color: var(--gray-light);
  transform: translateY(-3px);
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.1);
}

/* 卡片樣式 */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 20px rgba(0, 0, 0, 0.12);
}

/* 代碼區塊樣式增強 */
pre[class*="language-"] {
  border-radius: 0.5rem;
  margin: 1.5rem 0;
  position: relative;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

pre[class*="language-"]:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 響應式調整 - 手機端 (<768px) */
@media (max-width: 767px) {
  h1 {
    font-size: 1.875rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  h3 {
    font-size: 1.25rem;
  }

  .section-padding {
    padding: 2.5rem 0;
  }

  #hero .btn {
    width: 100%;
    margin-bottom: 0.75rem;
  }

  #hero img {
    max-width: 90%;
    margin: 0 auto;
  }

  .card-container {
    grid-template-columns: 1fr;
  }

  #features .rounded-lg {
    margin-bottom: 1.5rem;
  }

  .mockup-code {
    font-size: 0.8rem;
    padding: 1rem;
  }

  .copy-cmd-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

/* 平板端 (768px-1024px) */
@media (min-width: 768px) and (max-width: 1023px) {
  h1 {
    font-size: 2.25rem;
  }

  h2 {
    font-size: 1.75rem;
  }

  .section-padding {
    padding: 3rem 0;
  }

  #features .grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .workflow-step {
    margin-bottom: 1.5rem;
  }
}

/* 低性能設備動畫降級 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 輔助類 */
.text-gradient {
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--secondary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-title {
  position: relative;
  display: inline-block;
  margin-bottom: 2rem;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 50%;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--secondary-color)
  );
}

/* GitHub圖標 */
header nav a[href*="github"] svg {
  transition: transform 0.3s ease;
}

header nav a[href*="github"]:hover svg {
  transform: rotate(10deg);
}

/* 自定義滾動條 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-light);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-medium);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-dark);
}

/* 功能卡片樣式 */
#features .rounded-lg {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 1;
}

#features .rounded-lg::before {
  content: "";
  position: absolute;
  top: -100%;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.2)
  );
  z-index: -1;
  transition: all 0.5s ease;
}

#features .rounded-lg:hover::before {
  top: 0;
}

#features .rounded-lg img {
  transition: all 0.5s ease;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

#features .rounded-lg:hover img {
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.2));
}

#features .rounded-lg h3 {
  transition: all 0.3s ease;
  position: relative;
}

#features .rounded-lg h3::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: currentColor;
  transition: width 0.3s ease;
}

#features .rounded-lg:hover h3::after {
  width: 100%;
}

/* 根據顏色設置不同顏色光暈 */
#features .bg-indigo-50 {
  box-shadow: 0 0 0 transparent;
  transition: box-shadow 0.3s ease;
}

#features .rounded-lg:hover .bg-indigo-50 {
  box-shadow: 0 0 15px rgba(79, 70, 229, 0.2);
}

#features .bg-green-50 {
  box-shadow: 0 0 0 transparent;
  transition: box-shadow 0.3s ease;
}

#features .rounded-lg:hover .bg-green-50 {
  box-shadow: 0 0 15px rgba(16, 185, 129, 0.2);
}

#features .bg-blue-50 {
  box-shadow: 0 0 0 transparent;
  transition: box-shadow 0.3s ease;
}

#features .rounded-lg:hover .bg-blue-50 {
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.2);
}

#features .bg-amber-50 {
  box-shadow: 0 0 0 transparent;
  transition: box-shadow 0.3s ease;
}

#features .rounded-lg:hover .bg-amber-50 {
  box-shadow: 0 0 15px rgba(217, 119, 6, 0.2);
}

#features .bg-purple-50 {
  box-shadow: 0 0 0 transparent;
  transition: box-shadow 0.3s ease;
}

#features .rounded-lg:hover .bg-purple-50 {
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.2);
}

#features .bg-red-50 {
  box-shadow: 0 0 0 transparent;
  transition: box-shadow 0.3s ease;
}

#features .rounded-lg:hover .bg-red-50 {
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.2);
}

/* 工作流程區域樣式 */
#workflow {
  position: relative;
  overflow: hidden;
}

#workflow::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f3f4f6' fill-opacity='1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.2;
  pointer-events: none;
}

/* 桌面版時間軸樣式 */
.workflow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  width: 150px;
  transition: all 0.3s ease;
}

.workflow-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.workflow-step:hover .workflow-icon {
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.workflow-step:hover h4 {
  transform: translateY(-2px);
}

/* 移動版時間軸樣式 */
.workflow-icon-mobile {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  z-index: 1;
  transition: all 0.3s ease;
}

@keyframes verticalLineGrow {
  from {
    height: 0;
  }
  to {
    height: 40px;
  }
}

/* 步驟詳情彈窗樣式 */
#workflow-detail-modal {
  transition: opacity 0.3s ease;
}

#workflow-detail-modal.active {
  opacity: 1;
  pointer-events: auto;
}

.workflow-detail-link {
  position: relative;
  display: inline-block;
  transition: all 0.3s ease;
}

.workflow-detail-link::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: currentColor;
  transition: width 0.3s ease;
}

.workflow-detail-link:hover::after {
  width: 100%;
}

/* 安裝與配置區樣式 */
#installation .install-tab-btn {
  transition: all 0.3s ease;
  color: var(--gray-dark);
}

#installation .install-tab-btn.active {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  background-color: var(--gray-light);
}

#installation .install-tab-btn:hover:not(.active) {
  color: var(--primary-dark);
  background-color: var(--gray-light);
}

#installation .install-tab-btn svg {
  transition: transform 0.3s ease;
}

#installation .install-tab-btn:hover svg,
#installation .install-tab-btn.active svg {
  transform: scale(1.2);
}

/* 命令行介面樣式 */
.mockup-code {
  font-family: "Fira Code", monospace;
  background-color: #1a202c; /* 深色背景 */
  color: #e2e8f0; /* 亮色文本 */
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.mockup-code::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(to right, #ff6b6b, #4ecdc4, #ffbe0b);
  opacity: 0.7;
}

.mockup-code pre {
  margin: 0;
  padding: 0;
  overflow-x: auto;
}

.mockup-code code {
  display: block;
  line-height: 1.6;
}

.copy-cmd-btn {
  transition: all 0.3s ease;
  opacity: 0.7;
}

.copy-cmd-btn:hover {
  opacity: 1;
  transform: translateY(-2px);
}

/* Cursor IDE 配置選項卡 */
.cursor-tab-btn {
  transition: all 0.3s ease;
  background-color: #f3f4f6;
}

.cursor-tab-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* 安裝卡片樣式 */
#installation .grid > div {
  transition: all 0.3s ease;
  background-size: 200% 200%;
  animation: gradientAnimation 15s ease infinite;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

#installation .bg-blue-100,
#installation .bg-green-100,
#installation .bg-amber-100 {
  transition: all 0.3s ease;
}

#installation a {
  position: relative;
  display: inline-block;
}

#installation a::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: currentColor;
  transition: width 0.3s ease;
}

#installation a:hover::after {
  width: 100%;
}

@media (max-width: 768px) {
  #installation .install-tab-btn {
    font-size: 0.75rem;
    padding: 0.75rem 0.5rem;
  }

  #installation .install-tab-btn svg {
    margin-right: 0.25rem;
    height: 1rem;
    width: 1rem;
  }

  .mockup-code {
    padding: 1rem;
    font-size: 0.75rem;
  }

  .copy-cmd-btn {
    top: 0.5rem;
    right: 0.5rem;
  }
}

/* CTA與資源區樣式 */
#cta {
  position: relative;
  overflow: hidden;
}

#cta::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.4;
  pointer-events: none;
}

/* CTA按鈕增強 */
.btn-lg {
  padding: 0.875rem 1.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.btn-lg:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* 資源卡片樣式 */
.resource-card {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  border-radius: 0.5rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.resource-card:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.resource-link {
  position: relative;
  padding-left: 1rem;
}

.resource-link::before {
  content: "→";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: all 0.3s ease;
}

.resource-link a {
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  text-decoration: none;
}

.resource-link:hover a {
  color: white;
  padding-left: 0.5rem;
}

.resource-link:hover::before {
  opacity: 1;
}

/* 使用統計卡片 */
#cta .text-center {
  transition: all 0.3s ease;
  overflow: hidden;
}

#cta .text-center:hover {
  transform: translateY(-5px);
  background-color: rgba(255, 255, 255, 0.15);
}

#cta .text-3xl {
  position: relative;
  display: inline-block;
}

#cta .text-3xl::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background-color: white;
  transition: width 0.3s ease;
}

#cta .text-center:hover .text-3xl::after {
  width: 80%;
}

/* 頁腳樣式 */
footer {
  position: relative;
  overflow: hidden;
}

footer .text-gradient {
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.contributor-avatar {
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.contributor-avatar:hover {
  transform: translateY(-3px) scale(1.1);
  border-color: var(--primary-color);
  z-index: 10;
}

footer h3 {
  position: relative;
  display: inline-block;
}

footer h3::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--primary-color);
}

footer ul li {
  transition: all 0.2s ease;
}

footer ul li:hover {
  transform: translateX(5px);
}

@media (max-width: 768px) {
  .resource-card {
    margin-bottom: 1rem;
  }

  #cta .grid {
    gap: 1.5rem;
  }

  .btn-lg {
    width: 100%;
    margin-bottom: 0.75rem;
  }

  footer .grid {
    gap: 2rem;
  }
}

/* 語言切換按鈕 */
.language-switcher {
  display: flex;
  align-items: center;
  margin-left: 1rem;
}

.lang-btn {
  background: transparent;
  border: 1px solid rgba(203, 213, 225, 0.5);
  color: var(--gray-dark);
  font-size: 0.85rem;
  padding: 0.25rem 0.5rem;
  margin-left: 0.25rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
}

.lang-btn:first-child {
  margin-left: 0;
}

.lang-btn:hover {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.lang-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* 在移動設備上調整語言切換按鈕 */
@media (max-width: 767px) {
  .language-switcher {
    margin: 0.5rem 0;
    justify-content: center;
    width: 100%;
  }

  .lang-btn {
    padding: 0.4rem 0.75rem;
    font-size: 0.9rem;
  }
}
