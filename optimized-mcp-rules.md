# MCP Tools - Ultra-Optimized Descriptions

## Core Protocol
```
REQ_TYPE → ACTION
code/debug → analyze_zen/debug_zen → 2nd_opinion → store → feedback  
task/plan → plan_task → validate → feedback → execute → verify
simple → chat_zen → store → feedback
```

## Tool Descriptions (Compressed)

### plan_task
`Task planning guide. Use all available tools to gather real info. No assumptions/guesses.`

### analyze_task  
`Deep analysis + codebase check. Tech feasibility + risks. Use pseudocode only.`

### reflect_task
`Critical review of analysis. Assess completeness + identify optimization opportunities.`

### split_tasks
`Break complex→subtasks. Rules: 1-2d/task, <10 tasks, 3-level max, deps marked.
updateMode: append|overwrite|selective|clearAllTasks
JSON: no comments, escape quotes.
For low-intelligence models: clear instructions + interfaces + dependencies.`

### list_tasks
`Structured task list w/ status tracking, priorities, dependencies.`

### execute_task
`Get task guidance. Tool guides you - doesn't execute. Follow returned instructions.`

### verify_task
`Score task: Requirements(30%) + Tech(30%) + Integration(20%) + Performance(20%).
≥80 = complete. <80 = needs fixes.`

### delete_task
`Remove incomplete tasks only. Preserves completed tasks.`

### clear_all_tasks
`Reset all incomplete tasks. Creates backup.`

### update_task
`Modify task content. Completed tasks: summary+files only.`

### query_task
`Search tasks by keywords/ID. Returns abbreviated info.`

### get_task_detail
`Full task details by ID. Includes untruncated implementation guide + verification criteria.`

### process_thought
`Flexible thinking process. Build→question→verify→correct ideas.
When data needed: check project code first, then web search.
nextThoughtNeeded=false when sufficient, else adjust total_thoughts.`

### init_project_rules
`Initialize/update project standards when user requests project rules generation.`

### research_mode
`Deep programming research mode. Guides systematic web search + code analysis.
Prevents topic drift. For tech research, best practices, solution comparison.`

## Zen Integration Hooks

### analyze_zen → analyze_task
Auto-route code analysis requests to analyze_task with enhanced context.

### debug_zen → reflect_task  
Route debugging requests to reflect_task for critical analysis.

### chat_zen → process_thought
Route simple queries to process_thought for structured thinking.

## Auto-Detection Patterns
- `/analyze|debug|fix/i` → analyze_zen
- `/plan|task|build/i` → plan_task  
- `/help|how|what/i` → chat_zen

## Optimization Stats
- Original: ~2,200 tokens
- Optimized: ~600 tokens  
- Reduction: 73% (1,600 tokens saved)

## Implementation Notes
- Maintain full functionality
- Preserve all tool capabilities
- Add zen routing layer
- Keep existing schemas
- Integrate with current prompt system
