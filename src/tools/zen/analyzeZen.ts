import { z } from "zod";
import { analyzeTask, analyzeTaskSchema } from "../task/analyzeTask.js";

// Zen analysis tool - routes to analyze_task with enhanced context
export const analyzeZenSchema = z.object({
  query: z.string().min(1).describe("Analysis request or code/debug query"),
  context: z.string().optional().describe("Additional context or codebase info"),
});

export async function analyzeZen({
  query,
  context = "",
}: z.infer<typeof analyzeZenSchema>) {
  // Auto-generate summary and initial concept for analyze_task
  const summary = `Code Analysis: ${query.substring(0, 100)}${query.length > 100 ? '...' : ''}`;
  const initialConcept = `${query}${context ? `\n\nContext: ${context}` : ''}`;

  // Route to analyze_task
  return analyzeTask({
    summary,
    initialConcept,
  });
}
