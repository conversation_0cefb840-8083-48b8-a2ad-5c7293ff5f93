import { z } from "zod";
import { processThought, processThoughtSchema } from "../thought/processThought.js";

// Zen chat tool - routes to process_thought for structured thinking
export const chatZenSchema = z.object({
  query: z.string().min(1).describe("Question or help request"),
  stage: z.string().optional().default("Problem Definition").describe("Thinking stage"),
});

export async function chatZen({
  query,
  stage = "Problem Definition",
}: z.infer<typeof chatZenSchema>) {
  // Route to process_thought for structured response
  return processThought({
    thought: query,
    thought_number: 1,
    total_thoughts: 1,
    next_thought_needed: false,
    stage,
  });
}
