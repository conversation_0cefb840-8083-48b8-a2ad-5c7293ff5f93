import { z } from "zod";
import { reflectTask, reflectTaskSchema } from "../task/reflectTask.js";

// Zen debug tool - routes to reflect_task for critical analysis
export const debugZenSchema = z.object({
  issue: z.string().min(1).describe("Debug issue or problem description"),
  analysis: z.string().optional().describe("Initial analysis or investigation"),
});

export async function debugZen({
  issue,
  analysis = "",
}: z.infer<typeof debugZenSchema>) {
  // Auto-generate summary and analysis for reflect_task
  const summary = `Debug Issue: ${issue.substring(0, 100)}${issue.length > 100 ? '...' : ''}`;
  const fullAnalysis = analysis || `Debug investigation for: ${issue}`;

  // Route to reflect_task for critical debugging analysis
  return reflectTask({
    summary,
    analysis: fullAnalysis,
  });
}
