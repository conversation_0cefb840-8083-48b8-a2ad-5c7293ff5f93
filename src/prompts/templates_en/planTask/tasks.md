### Existing Task References

#### Completed Tasks

{completedTasks}

#### Incomplete Tasks

{unfinishedTasks}

#### Task Adjustment Principles

1. **Completed Task Protection** - Completed tasks cannot be modified or deleted
2. **Incomplete Task Adjustability** - Incomplete tasks can be modified based on new requirements
3. **Task ID Consistency** - Must use original IDs when referencing existing tasks
4. **Dependency Relationship Integrity** - Avoid circular dependencies, do not depend on tasks marked for removal
5. **Task Continuity** - New tasks should form a coherent whole with existing tasks
