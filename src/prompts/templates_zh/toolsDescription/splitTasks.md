將複雜任務分解為獨立子任務，建立依賴關係和優先順序。

1. **拆分粒度控制（必讀）**

   - **最小可交付單元（Minimum Viable Task）**  
     每個子任務應能在 **1–2 個工作天**（約 8–16 小時）內，由一名開發者完成並驗收。
   - **最大複雜度限制**  
     單個子任務不應同時跨越「前端」「後端」「資料庫」等多個技術領域；若需跨域，請拆成多個子任務。
   - **任務數量建議**  
     一次拆分不超過 **10 項子任務**；若超過，依優先順序分批（每批 6–8 項）提交。
   - **任務長度建議**
     一次拆分的參數不超過 5000 字，如果超過請分批提交。
   - **深度層級限制**  
     任務樹深度不宜超過 **3 層**（第 1 層：功能模組；第 2 層：主要流程；第 3 層：關鍵步驟）。

2. **拆分步驟示例**

   - 識別「核心功能點」，為每個功能點創建子任務
   - 在每個子任務下標註「輸入／輸出」與「驗收標準」
   - 如果需要請提供「pseudocode」，僅提供高級邏輯流程和關鍵步驟避免完整代碼
   - 檢查子任務間「前後依賴」，並在 `dependencies` 欄位標明
   - 若任務涉及介面設計，請務必提供完整且一致的定義，包括：

     - function / class / schema 定義（含名稱、參數、回傳值）
     - 各項目之資料型別、用途描述、是否為選填
     - 錯誤處理方式與預期異常情境
     - 依賴關係與命名規範（如有）
     - 範例資料與使用方式

     這將有助於任務之間的連貫性、可讀性與開發精確性。

3. **依賴與優先順序**

   - 標註每個子任務的 `dependencies`（依賴清單）
   - 根據依賴圖自動計算並執行優先順序，確保關鍵路徑優先

4. **更新模式說明 (`updateMode`)**

   當你需要建立一個全新的任務並且與目前的任務列表無關時，請務必使用`clearAllTasks`避免任務混肴

   - `append`：保留現有未完成任務並添加新任務
   - `overwrite`：刪除所有未完成任務，保留已完成狀態的任務
   - `selective`：根據任務名稱智能匹配並更新相應任務
   - `clearAllTasks`：清除所有任務並自動備份現有列表

5. **JSON 嚴謹規則**

   - **禁止註解**：JSON 本身不支援註解，任何 `#` 或 `//` 都會導致解析失敗
   - **注意轉義**：所有特殊字元（如雙引號 `\"`、反斜線 `\\`）必須正確轉義，否則視為非法字元

6. **重要訊息**

這些任務將會分配給**低智能模型**執行，所以你必須參考以下幾點

- `明確且清楚的指導`：這將避免**低智能模型**設計出錯誤或架構風格不一致的程式碼，所以請給予明確的指令或規範
- `封裝接口`：每個任務都會是獨立執行，所以需要定義好接口，例如你會暴露出什麼 function name 有什麼參數，會回傳什麼等等的資訊，方便其他任務執行模型可以快速的知道如何使用或穿接相關功能
- `依賴性`：如果任務與任務之間有依賴性，那應該是先定義好交互的介面，任務之間不需要知道各自的實作，但需要知道如何與對方交互
