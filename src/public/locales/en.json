{"app_title": "Shrimp Task Manager", "status_indicator_online": "ONLINE", "dependency_view_title": "Dependency View", "dependency_graph_placeholder": "Dependency relationship for all tasks", "reset_view_btn_title": "Reset View", "task_list_title": "Task List", "search_placeholder": "Search tasks...", "sort_option_date_desc": "Creation Time (New-Old)", "sort_option_date_asc": "Creation Time (Old-New)", "sort_option_name_asc": "Name (A-Z)", "sort_option_name_desc": "Name (Z-A)", "sort_option_status": "Status", "status_filter_all": "All Statuses", "status_filter_pending": "Pending", "status_filter_in_progress": "In Progress", "status_filter_completed": "Completed", "task_list_loading": "Loading...", "task_list_empty": "No matching tasks", "task_details_title": "Task Details", "task_details_placeholder": "Select a task to view details", "footer_copyright": "© 2023 Shrimp Task Manager - Current time: ", "status_pending": "Pending", "status_in_progress": "In Progress", "status_completed": "Completed", "task_detail_status_label": "Status:", "task_detail_summary_title": "Completion Summary", "task_detail_description_title": "Task Description", "task_detail_implementation_guide_title": "Implementation Guide", "task_detail_verification_criteria_title": "Verification Criteria", "task_detail_dependencies_title": "Dependencies (Prerequisites)", "task_detail_related_files_title": "Related Files", "task_detail_notes_title": "Notes", "task_detail_no_summary": "No summary provided.", "task_detail_no_description": "No description", "task_detail_no_implementation_guide": "No implementation guide", "task_detail_no_verification_criteria": "No verification criteria", "task_detail_no_dependencies": "No dependencies", "task_detail_no_related_files": "No related files", "task_detail_no_notes": "No notes", "task_detail_unknown_dependency": "Unknown Task", "progress_completed": "Completed", "progress_in_progress": "In Progress", "progress_pending": "Pending", "progress_total": "Total", "global_analysis_title": "Goal", "error_loading_tasks": "Failed to load tasks: {message}", "error_updating_tasks": "Failed to update tasks: {message}", "error_loading_graph": "Failed to load dependency graph", "error_task_not_found": "Task not found"}