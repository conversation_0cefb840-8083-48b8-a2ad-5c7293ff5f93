# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
FROM node:lts-alpine

# Create app directory
WORKDIR /mcp-shrimp-task-manager

# Install app dependencies
COPY package*.json ./

# Install dependencies without running lifecycle scripts
RUN npm install --ignore-scripts

# Bundle app source code
COPY . .

# Build the TypeScript code
RUN npm run build

# Expose port if necessary (not required by MCP over stdio)

# Command to run the MCP server
CMD [ "npm", "run", "start" ]
